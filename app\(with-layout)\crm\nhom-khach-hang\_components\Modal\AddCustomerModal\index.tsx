import { useSearchCustomer } from '@/apis/customer/customer.api';
import {
    SearchCustomer,
    SearchCustomerResponse,
} from '@/apis/customer/customer.type';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { LeadStatus as LeadStatusData } from '@/constants/sharedData/sharedData';
import { useState, useEffect } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    But<PERSON>,
    Card,
    CardHeader,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Table,
    Input,
} from 'reactstrap';

interface AddCustomerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: (selectedCustomers: SearchCustomerResponse[]) => void;
}

const AddCustomerModal = ({
    isOpen,
    toggle,
    onSuccess,
}: AddCustomerModalProps) => {
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedData, setSelectedData] = useState<SearchCustomerResponse[]>(
        [],
    );
    const methods = useForm<SearchCustomer>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
        },
    });
    const { control, setValue } = methods;
    const [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'IndustryId',
            'BusinessType',
            'LeadStatus',
            'SalePerson',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });
    const { data, isLoading } = useSearchCustomer({
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });
    const { items: listCustomer = [], totalItems = 0 } = data ?? {};
    const formattedCustomer = listCustomer.map(
        (Customer: SearchCustomerResponse) => ({
            ...Customer,
            createdOn: Customer.createdOn
                ? new Date(
                      Customer.createdOn.substring(0, 10),
                  ).toLocaleDateString('vi-VN')
                : '',
        }),
    );

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Reset page when search params change
    useEffect(() => {
        setCurrentPage(1);
    }, [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
    ]);

    // Calculate pagination
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentData = formattedCustomer.slice(startIndex, endIndex);

    // Handle checkbox selection
    const handleSelectCustomer = (customer: SearchCustomerResponse) => {
        const isSelected = selectedIds.includes(customer.id);
        if (isSelected) {
            setSelectedIds(selectedIds.filter((id) => id !== customer.id));
            setSelectedData(
                selectedData.filter((item) => item.id !== customer.id),
            );
        } else {
            setSelectedIds([...selectedIds, customer.id]);
            setSelectedData([...selectedData, customer]);
        }
    };

    // Helper function to get lead status label
    const getLeadStatusLabel = (leadStatus: string) => {
        // Debug: Log the values to understand the data
        if (leadStatus !== undefined && leadStatus !== null) {
            // Uncomment for debugging:
            // console.log('leadStatus:', leadStatus, 'type:', typeof leadStatus);
            // console.log('LeadStatusData values:', LeadStatusData.map(s => s.value));
        }

        // Try multiple matching strategies
        let status = LeadStatusData.find(
            (s) => String(s.value) === String(leadStatus),
        );

        // If not found, try direct number comparison
        if (!status && !isNaN(Number(leadStatus))) {
            status = LeadStatusData.find(
                (s) => Number(s.value) === Number(leadStatus),
            );
        }

        // If still not found, try case-insensitive string comparison
        if (!status && typeof leadStatus === 'string') {
            status = LeadStatusData.find(
                (s) => s.label.toLowerCase() === leadStatus.toLowerCase(),
            );
        }

        return status?.label || leadStatus || 'N/A';
    };

    // Handle select all checkbox
    const handleSelectAll = () => {
        if (
            selectedIds.length === currentData.length &&
            currentData.length > 0
        ) {
            setSelectedIds([]);
            setSelectedData([]);
        } else {
            const allIds = currentData.map((item) => item.id);
            setSelectedIds(allIds);
            setSelectedData(currentData);
        }
    };

    const handleCancel = () => {
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    const handleSave = () => {
        if (onSuccess && selectedData.length > 0) {
            onSuccess(selectedData);
        }
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='xl'>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>Thêm khách hàng</ModalHeader>
                <ModalBody>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex flex-wrap align-items-center gap-2'>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                    />
                                </div>
                            </CardHeader>

                            {isLoading ? (
                                <div className='text-center p-4'>
                                    <div
                                        className='spinner-border'
                                        role='status'
                                    >
                                        <span className='visually-hidden'>
                                            Loading...
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <Table responsive striped bordered hover>
                                        <thead>
                                            <tr>
                                                <th style={{ width: '50px' }}>
                                                    <Input
                                                        type='checkbox'
                                                        checked={
                                                            selectedIds.length ===
                                                                currentData.length &&
                                                            currentData.length >
                                                                0
                                                        }
                                                        onChange={
                                                            handleSelectAll
                                                        }
                                                        style={{
                                                            accentColor: '#4dc7b6',
                                                        }}
                                                    />
                                                </th>
                                                <th>Tên khách hàng</th>
                                                <th>Loại hình</th>
                                                <th>Lĩnh vực</th>
                                                <th>Giai đoạn</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {currentData.length > 0 ? (
                                                currentData.map((customer) => {
                                                    const isSelected =
                                                        selectedIds.includes(
                                                            customer.id,
                                                        );

                                                    return (
                                                        <tr key={customer.id}>
                                                            <td>
                                                                <Input
                                                                    type='checkbox'
                                                                    checked={
                                                                        isSelected
                                                                    }
                                                                    onChange={() =>
                                                                        handleSelectCustomer(
                                                                            customer,
                                                                        )
                                                                    }
                                                                    style={{
                                                                        accentColor: '#4dc7b6',
                                                                    }}
                                                                />
                                                            </td>
                                                            <td>
                                                                {customer.name}
                                                            </td>
                                                            <td>
                                                                {
                                                                    customer.businessTypeName
                                                                }
                                                            </td>
                                                            <td>
                                                                {
                                                                    customer.industryName
                                                                }
                                                            </td>
                                                            <td>
                                                                <span
                                                                    className='badge me-1'
                                                                    style={{
                                                                        backgroundColor:
                                                                            '#daf4f0',
                                                                        color: '#2fbeab',
                                                                        display:
                                                                            'inline-block',
                                                                        textAlign:
                                                                            'center',
                                                                        padding:
                                                                            '4px 8px',
                                                                        fontSize:
                                                                            '12px',
                                                                        fontWeight: 500,
                                                                        borderRadius:
                                                                            '4px',
                                                                    }}
                                                                >
                                                                    {getLeadStatusLabel(
                                                                        customer.leadStatus,
                                                                    )}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    );
                                                })
                                            ) : (
                                                <tr>
                                                    <td
                                                        colSpan={6}
                                                        className='text-center'
                                                    >
                                                        Không có dữ liệu
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </Table>

                                    {totalPages > 1 && (
                                        <div className='d-flex justify-content-between align-items-center mt-3'>
                                            <div>
                                                Hiển thị {startIndex + 1} -{' '}
                                                {Math.min(endIndex, totalItems)}{' '}
                                                của {totalItems} kết quả
                                            </div>
                                            <div className='d-flex gap-2'>
                                                <Button
                                                    style={{
                                                        backgroundColor:
                                                            '#4dc7b6',
                                                        border: 'none',
                                                    }}
                                                    size='sm'
                                                    disabled={currentPage === 1}
                                                    onClick={() => {
                                                        setCurrentPage(
                                                            currentPage - 1,
                                                        );
                                                        setValue(
                                                            'PageNumber',
                                                            currentPage - 1,
                                                        );
                                                    }}
                                                >
                                                    <i className='ri-arrow-left-s-line'></i>
                                                </Button>
                                                <span className='align-self-center'>
                                                    Trang {currentPage} /{' '}
                                                    {totalPages}
                                                </span>
                                                <Button
                                                    style={{
                                                        backgroundColor:
                                                            '#4dc7b6',
                                                        border: 'none',
                                                    }}
                                                    size='sm'
                                                    disabled={
                                                        currentPage ===
                                                        totalPages
                                                    }
                                                    onClick={() => {
                                                        setCurrentPage(
                                                            currentPage + 1,
                                                        );
                                                        setValue(
                                                            'PageNumber',
                                                            currentPage + 1,
                                                        );
                                                    }}
                                                >
                                                    <i className='ri-arrow-right-s-line'></i>
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </>
                            )}
                        </Card>
                    </Col>
                </ModalBody>
                <ModalFooter>
                    <Button color='danger' onClick={handleCancel}>
                        Huỷ
                    </Button>
                    <Button
                        color='success'
                        onClick={handleSave}
                        disabled={selectedData.length === 0}
                    >
                        Lưu
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddCustomerModal;
